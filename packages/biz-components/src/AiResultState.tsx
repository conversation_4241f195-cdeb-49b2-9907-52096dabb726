import { View, Text } from '@tarojs/components';
import { Image } from '@jgl/components';
import { fontSizeStyle } from '@jgl/biz-func';
import { ReactNode, useCallback, useMemo, useRef } from 'react';
import Icon from '@jgl/icon';
import { getCurrentPages, switchTab } from '@tarojs/taro';

import {
  PhotoDescriptionPopup,
  PhotoDescriptionPopupRef,
} from './PhotoDescriptionPopup';

type Props = {
  /**
   * 原图src
   */
  imageSrc?: string | undefined;
  isLoading?: boolean;
  error?: string | undefined;
  loadingText?: string;
  empty?: string;
  isEmpty?: boolean;
  imageNode?: ReactNode;
};

/**
 * AiResultState Ai业务的loading和加载失败组件
 *
 * @component
 * @example
 * return (
 *   <AiResultState />
 * )
 */
export const AiResultState = (props: Props) => {
  const { imageNode, isLoading, error, loadingText, empty, isEmpty } = props;
  /**
   * 拍照说明ref
   */
  const photoDescriptionPopupRef = useRef<PhotoDescriptionPopupRef>(null);

  const handleClickDescription = useCallback(() => {
    photoDescriptionPopupRef?.current?.toggle();
  }, []);

  const showBackHome = getCurrentPages().length === 1;

  const handleBackHome = useCallback(() => {
    switchTab({ url: `/pages/index/index` });
  }, []);

  /**
   * 渲染错误或者空态
   */
  const renderEmptyOrError = useMemo(() => {
    return (
      <View className='flex-center h-full w-full flex-col'>
        <View className=' flex flex-col items-center'>
          <Image src={Icon.photoFail} className='h-[92PX] w-[187PX]' />
          <Text className='text-[#333333]' style={fontSizeStyle[14]}>
            {empty || error || '暂未识别出题目，再拍一次吧'}
          </Text>
          <View
            className='flex-center mt-[4PX] gap-x-[2PX]'
            onClick={handleClickDescription}
          >
            <Text
              className='font-normal text-[#8C8C8C]'
              style={fontSizeStyle[14]}
            >
              如何拍照？
            </Text>
            <Text
              className='font-normal text-[#3ca6fe]'
              style={fontSizeStyle[14]}
            >
              查看帮助
            </Text>
          </View>
        </View>
        <View className='flex-start mt-[56PX] flex flex-col'>
          <Text
            className='leading-[30PX] text-[#666666]'
            style={fontSizeStyle[14]}
          >
            手机与纸张平行
          </Text>
          <Text
            className='leading-[30PX] text-[#666666]'
            style={fontSizeStyle[14]}
          >
            纸张平放，不要倾斜
          </Text>
          <Image
            src={Icon.photoExample}
            className='mt-[40PX] h-[94PX] w-[97PX]'
          />
        </View>
        {showBackHome ? (
          <View
            className='flex-center w-full py-[24PX]'
            onClick={handleBackHome}
          >
            <Image src={Icon.oralCheckBanner} className='h-[88PX] w-full ' />
          </View>
        ) : null}
      </View>
    );
  }, [empty, error, handleBackHome, handleClickDescription, showBackHome]);

  const renderStatus = useMemo(() => {
    if (isLoading) {
      // loading状态 需要支持展示原图
      return (
        <>
          {imageNode ?? null}
          <View className='flex-center absolute left-0 top-0 h-full w-full flex-col'>
            <View className='flex-cent er absolute left-0 top-0 h-full w-full flex-col bg-black opacity-60' />
            <Image
              src={Icon.takePhotoReadingRecognizing}
              className='relative z-10 h-[267PX] w-[267PX]'
            />

            <Text
              className='relative z-10 font-normal leading-[24PX] text-white'
              style={fontSizeStyle[16]}
            >
              {loadingText || '咕噜正在识别中...'}
            </Text>
          </View>
        </>
      );
    } else if (isEmpty || error) {
      return renderEmptyOrError;
    }
    return null;
  }, [error, imageNode, isEmpty, isLoading, loadingText, renderEmptyOrError]);
  return (
    <View className='flex-center relative h-full w-full flex-col'>
      {renderStatus}

      <PhotoDescriptionPopup ref={photoDescriptionPopupRef} />
    </View>
  );
};
