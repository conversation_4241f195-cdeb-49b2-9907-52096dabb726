import { PhotoAndAnalysis, usePhotoQuestionAnswer } from '@bookln/ai-explain';
import {
  YTImage,
  YTTouchable,
  YTXStack,
} from '@bookln/cross-platform-components';
import { ChevronLeftIcon } from '@bookln/icon-lucide';
import { PermissionPurposeScene } from '@bookln/permission';
import { PhotoProgress } from '@jgl/biz-func';
import { router, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { PhotoQuestionAnswerResult } from '@bookln/biz-components-rojer-katex-mini';

/**
 * AI讲解
 */
export default function AiExplainScreen() {
  const {
    // markdownResult,
    imageUrl,
    reviewImageUrl,
    progress,
    boxList,
    loading,
    error,
    onAnalysisImage,
    updateProgress,
    onPressTakePhoto,
    onPressBack,
  } = usePhotoQuestionAnswer();

  return (
    <>
      <Stack.Screen
        options={{
          headerBackVisible: false,
          headerTransparent: true,
          headerLeft: () => {
            return (
              <YTTouchable onPress={() => router.back()}>
                <YTImage
                  source={require('../assets/images/icon_back_white_bg_black_line.png')}
                  w={32}
                  h={32}
                />
                {/* <ChevronLeftIcon
                  color={
                    progress === PhotoProgress.Result &&
                    (loading || error !== undefined)
                      ? 'black'
                      : 'white'
                  }
                /> */}
              </YTTouchable>
            );
          },
          title: '',
        }}
      />
      <StatusBar style='light' />
      <YTXStack flex={1}>
        {progress !== PhotoProgress.Result && (
          <PhotoAndAnalysis
            progress={progress}
            onAnalysisImage={onAnalysisImage}
            updateProgress={updateProgress}
            scene={PermissionPurposeScene.AiExplain}
            title='AI讲解'
          />
        )}

        {progress === PhotoProgress.Result && (
          <PhotoQuestionAnswerResult
            imageUrl={imageUrl}
            reviewImageUrl={reviewImageUrl}
            loading={loading}
            visible={true}
            boxList={boxList}
            onPressTakePhoto={onPressTakePhoto}
            onPressBack={onPressBack}
            error={error}
            bizCode='jinggulu'
          />
        )}
      </YTXStack>
    </>
  );
}
