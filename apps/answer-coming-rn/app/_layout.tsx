import {
  YTFontScaleProvider,
  YTTouchable,
} from '@bookln/cross-platform-components';
import { ChevronLeftIcon } from '@bookln/icon-lucide';
import { ActionSheetProvider } from '@expo/react-native-action-sheet';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { agreementStateAtom, store, tamaguiThemeNameAtom } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { JglThemeProvider } from '@jgl/ui-v4';
import { envVars } from '@jgl/utils';
import { ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { wrap } from '@sentry/react-native';
import {
  QueryProvider,
  usePlatformConfig,
} from '@yunti-private/net-query-hooks';
import { MemoryLogger } from '@yunti-private/rn-memory-logger';
import { RnUmeng } from '@yunti-private/rn-umeng';
import { useFonts } from 'expo-font';
import { router, Stack } from 'expo-router';
import { hideAsync, preventAutoHideAsync } from 'expo-splash-screen';
import { useAtom, useAtomValue } from 'jotai';
import { Suspense, useEffect, useMemo } from 'react';
import { LogBox, Platform } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { RootSiblingParent } from 'react-native-root-siblings';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import { FontLanguage, TamaguiProvider, Theme, useTheme } from 'tamagui';
import { SimpleSuspenseFallback } from '../components/SimpleSuspenseFallback';
import { useChangeRoot } from '../hooks/useChangeRoot';
import { useCheckAppUpgrade } from '../hooks/useCheckAppUpgrade';
import { useFontLanguage } from '../hooks/useFontLanguage';
import { useInitStorageAtom } from '../hooks/useInitStorageAtom';
import { useNavigationTheme } from '../hooks/useJglThemes';
import { useLoadAndRefreshUserInfo } from '../hooks/useLoadAndRefreshUserInfo';
import { useLoadAtomsOnLaunch } from '../hooks/useLoadAtomOnLaunch';
import { useLogInExpired } from '../hooks/useLogInExpired';
import { useScreenTrack } from '../hooks/useScreenTrack';
import { initAppBeforeRender, useInitApp } from '../init/initApp';
import { config as tamaguiConfig } from '../tamagui.config';
import { ChannelHelper } from '../utils/ChannelHelper';
import { useStartNetworkLogIfNeeded } from '../utils/networkLogUtil';

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from 'expo-router';

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: 'index',
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
preventAutoHideAsync();

initAppBeforeRender();

export function RootLayout() {
  // 默认开启 MemoryLogger
  MemoryLogger.enable(true);
  // const { initBeiZiAd } = useBeiZiAd();
  const agreementState = useAtomValue(agreementStateAtom);
  useStartNetworkLogIfNeeded();
  const [loaded, error] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    pinyin: require('../assets/fonts/pinyin.ttf'),
    'Alimama FangYuanTi VF': require('../assets/fonts/AlimamaFangYuanTiVF-Thin-2.ttf'),
    // DingTalkJinBuTi: require('../assets/fonts/DingTalk JinBuTi.ttf'),
    ...FontAwesome.font,
  });

  useEffect(() => {
    if (envVars.rnIgnoreLogBox()) {
      LogBox.ignoreAllLogs(true);
    }
  }, []);

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded && agreementState === 'agreed') {
      if (agreementState === 'agreed') {
        //初始化广告
        // initBeiZiAd({
        //   autoSplash: false,
        //   onInitSuccess: () => {
        //     // SplashScreen.hideAsync();
        //   },
        //   onInitError: () => {
        //     SplashScreen.hideAsync();
        //   },
        // });
        ChannelHelper.shared()
          .getChannel()
          .then((channel) => {
            const appKey = Platform.select({
              ios: '670a4b1980464b33f6d9c506',
              android: '670a4b04667bfe33f3bef460',
            });
            if (appKey) {
              RnUmeng.init({
                appKey,
                channel,
              });
            }
          });
      } else if (agreementState === 'disagreed') {
        hideAsync();
      }
    }
  }, [agreementState, loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <SafeAreaProvider>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <KeyboardProvider statusBarTranslucent navigationBarTranslucent>
          <Provider store={store}>
            <QueryProvider networking={container.net()}>
              <JglThemeProvider initialTheme={'light'}>
                <RootLayoutNav />
              </JglThemeProvider>
            </QueryProvider>
          </Provider>
        </KeyboardProvider>
      </GestureHandlerRootView>
    </SafeAreaProvider>
  );
}

function RootLayoutNav() {
  const agreementState = useAtomValue(agreementStateAtom);
  useInitStorageAtom();
  useInitApp();
  // useInitIAP();
  useLoadAtomsOnLaunch();
  useCheckAppUpgrade({ isActive: true });
  useScreenTrack();
  useLoadAndRefreshUserInfo();

  usePlatformConfig({ enabled: agreementState === 'agreed' });

  useLogInExpired();
  useChangeRoot();

  const font = useFontLanguage();

  const tamaguiThemeName = useAtomValue(tamaguiThemeNameAtom);

  return (
    <RootSiblingParent>
      <TamaguiProvider config={tamaguiConfig}>
        <YTFontScaleProvider>
          <FontLanguage body={font}>
            <ActionSheetProvider>
              <BottomSheetModalProvider>
                <Theme name={tamaguiThemeName}>
                  <Children />
                </Theme>
              </BottomSheetModalProvider>
            </ActionSheetProvider>
          </FontLanguage>
        </YTFontScaleProvider>
      </TamaguiProvider>
    </RootSiblingParent>
  );
}

const Children = () => {
  const navigationTheme = useNavigationTheme();
  const [agreementState] = useAtom(agreementStateAtom);
  const theme = useTheme();

  const navigationOptions = useMemo(() => {
    return {
      navigationBarTranslucent: true,
      navigationBarColor: '#00000000',

      // 统一设置背景色，避免透明
      contentStyle: { backgroundColor: '#FFFFFF' },
    };
  }, []);

  return (
    <NavigationThemeProvider value={navigationTheme}>
      <Stack
        screenOptions={{
          headerTitleAlign: 'center',
          headerLeft: () => {
            return (
              <YTTouchable onPress={() => router.back()}>
                <ChevronLeftIcon color={theme.color12.val} />
              </YTTouchable>
            );
          },
          headerShadowVisible: false,
          // 解决 Android 上因为 react-native-blur 导致的页面白屏问题
          // https://github.com/Kureev/react-native-blur/issues/595#issuecomment-**********
          animation: Platform.OS === 'android' ? 'none' : 'default',
          ...navigationOptions,
        }}
      >
        <Stack.Screen
          name='agreement'
          options={{
            ...navigationOptions,
            headerShown: false,
            headerTitle: '',
            headerShadowVisible: false,

            // disagreed 说明已经进入首页
            // 使用 modal 展示，并且加上动画
            animation: agreementState === 'undetermined' ? 'none' : undefined,
            presentation:
              agreementState === 'undetermined' ? undefined : 'modal',
          }}
        />
        <Stack.Screen
          name='(weChatOAuth)'
          options={{ ...navigationOptions, animation: 'none' }}
        />
        <Stack.Screen
          name='(tabs)'
          options={{
            ...navigationOptions,
            headerShown: false,
            animation: 'none',
          }}
        />
        <Stack.Screen
          name='(touristTabs)'
          options={{
            ...navigationOptions,
            headerShown: false,
            animation: 'none',
          }}
        />
        <Stack.Screen
          name='(logInModal)'
          options={{
            ...navigationOptions,
            headerShown: false,
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name='(bindPhoneModal)'
          options={{
            ...navigationOptions,
            headerShown: false,
            presentation: 'modal',
          }}
        />
      </Stack>
    </NavigationThemeProvider>
  );
};

const Root = () => {
  return (
    <Suspense fallback={<SimpleSuspenseFallback />}>
      <RootLayout />
    </Suspense>
  );
};

export default wrap(Root);
