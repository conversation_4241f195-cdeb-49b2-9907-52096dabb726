import { YTYStack } from '@bookln/cross-platform-components';
import { useImageSize } from '@jgl/biz-func';
import { useCallback, useEffect, useRef } from 'react';
import { StyleSheet, type LayoutChangeEvent } from 'react-native';
import Canvas from 'react-native-canvas';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  type SharedValue,
} from 'react-native-reanimated';
import { Image, View } from 'tamagui';

const AnimatedView = Animated.createAnimatedComponent(View);

type ImageViewerProps = {
  total: number;
  index: number;
  item: string;
  containerWidth: SharedValue<number>;
  containerHeight: SharedValue<number>;
};

export const AnswerImageViewer = (props: ImageViewerProps) => {
  const { total, index, item, containerWidth, containerHeight } = props;
  const imageWidth = useSharedValue(0);
  const imageHeight = useSharedValue(0);
  const scale = useSharedValue(1);
  const savedScale = useSharedValue(1); // 保存当前缩放比例
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const savedTranslateX = useSharedValue(0); // 保存当前偏移量 X
  const savedTranslateY = useSharedValue(0); // 保存当前偏移量 Y
  const overDragDistance = useSharedValue(0);
  const overDragDirection = useSharedValue<'left' | 'right' | null>(null);
  const { init, size } = useImageSize({ imageUrl: item });
  const canvasRef = useRef<Canvas>(null);

  const { imgWidth: displayWidth, imgHeight: displayHeight } = size;

  useEffect(() => {
    init();
  }, [init]);

  const panGesture = Gesture.Pan()
    .manualActivation(true)
    .onStart((e) => {
      if (scale.value > 1) {
        savedTranslateX.value = translateX.value;
        savedTranslateY.value = translateY.value;
      }
    })
    .onTouchesMove((e, state) => {
      if (scale.value > 1 && overDragDistance.value < 50) {
        state.activate();
      } else {
        if (
          (index === 0 && overDragDirection.value === 'right') ||
          (index === total - 1 && overDragDirection.value === 'left')
        ) {
          // 如果是第一个且滑动到边界后继续向右滑以及最后一个滑动到边界继续向左滑不处理
          state.activate();
          return;
        }
        scale.value = withSpring(1);
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        state.fail();
      }
    })
    .onUpdate((e) => {
      // 仅允许在放大时拖动
      if (scale.value > 1) {
        const maxTranslateX =
          (imageWidth.value - containerWidth.value / scale.value) / 2;
        const maxTranslateY =
          (imageHeight.value - containerHeight.value / scale.value) / 2;
        // const maxTranslateY =
        //   (imageHeight.value - imageHeight.value / scale.value) / 2;
        const newTranslateX =
          savedTranslateX.value + e.translationX / scale.value;
        const newTranslateY =
          savedTranslateY.value + e.translationY / scale.value;

        let overDrag = 0;
        let direction: 'left' | 'right' | null = null;

        // 水平边界检查
        if (newTranslateX > maxTranslateX) {
          overDrag = newTranslateX - maxTranslateX;
          direction = 'right';
          translateX.value = maxTranslateX;
        } else if (newTranslateX < -maxTranslateX) {
          overDrag = -maxTranslateX - newTranslateX;
          direction = 'left';
          translateX.value = -maxTranslateX;
        } else {
          translateX.value = newTranslateX;
        }

        // 垂直边界检查（不需要过度拖动）
        translateY.value = Math.min(
          Math.max(newTranslateY, -maxTranslateY),
          maxTranslateY,
        );

        overDragDistance.value = overDrag;
        overDragDirection.value = direction;
      }
    })
    .onEnd((e) => {
      if (scale.value === 1) {
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
      }
    });

  // 双击复位
  const doubleTap = Gesture.Tap()
    .numberOfTaps(2)
    .onEnd(() => {
      scale.value = withSpring(1);
      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
    });

  const pinchGesture = Gesture.Pinch()
    .onStart(() => {
      savedScale.value = scale.value;
    })
    .onUpdate((e) => {
      // 限制最小缩放比例（如 0.5）
      const minScale = 0.5;
      const newScale = savedScale.value * e.scale;
      scale.value = Math.max(newScale, minScale);
    })
    // 移除强制回到 1 的逻辑
    .onEnd(() => {
      overDragDistance.value = 0;
    });

  // 合并手势（双击 + 捏合 + 拖动）
  const gesture = Gesture.Simultaneous(
    doubleTap,
    Gesture.Simultaneous(pinchGesture, panGesture),
  );

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));

  const handleDraw = useCallback(
    (container: { width: number; height: number }) => {
      if (canvasRef.current) {
        const { width, height } = container;
        const canvasWidth = Math.sqrt(width ** 2 + height ** 2);
        const canvasHeight = height;
        canvasRef.current.width = canvasWidth;

        canvasRef.current.height = canvasHeight;

        const ctx = canvasRef.current.getContext('2d');
        // 设置文字样式
        ctx.font = '24px sans-serif';
        ctx.fillStyle = '#CCCCCC80';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.translate(width / 2, canvasHeight / 2);

        // 旋转45度（注意：rotate 使用的是弧度，45度 = π/4 弧度）
        ctx.rotate(-Math.PI / 4);
        ctx.translate(-width / 2, -canvasHeight / 2);

        const text = '答案来了';
        const spacing = canvasHeight / 5; // 文字间距

        // 计算文字宽度
        const textWidth = 40;

        // 计算每行可以容纳的文字数量
        const textsPerRow = Math.floor(canvasWidth / (textWidth + spacing));

        // 计算垂直方向可以容纳的行数
        const rows = Math.floor(canvasHeight / spacing);

        // 绘制文字
        for (let row = 0; row < rows; row++) {
          for (let col = 0; col < textsPerRow; col++) {
            const x = col * (textWidth + spacing) + textWidth / 2;
            const y = row * spacing + spacing / 2;
            ctx.fillText(text, x, y);
          }
        }
        ctx.restore();
      }
    },
    [],
  );

  const onLayout = useCallback(
    (event: LayoutChangeEvent) => {
      const { width, height } = event.nativeEvent.layout;
      imageWidth.value = width;
      imageHeight.value = height;
      handleDraw({ width, height });
    },
    [handleDraw, imageHeight, imageWidth],
  );

  return (
    <YTYStack overflow='hidden' flex={1} bg={'#F4F8FB'} p={8} jc='center'>
      <GestureDetector gesture={gesture}>
        <AnimatedView
          style={animatedStyle}
          justifyContent='flex-start'
          alignItems='center'
          position={'relative'}
          overflow={'hidden'}
          onLayout={onLayout}
        >
          <Image
            source={{ uri: item }}
            objectFit={'contain'}
            width={'100%'}
            aspectRatio={
              displayHeight > 0 ? displayWidth / displayHeight : undefined
            }
          />
          <YTYStack
            position='absolute'
            style={StyleSheet.absoluteFill}
            pointerEvents='none'
            bg={'transparent'}
          >
            <Canvas
              ref={canvasRef}
              style={{
                width: '100%',
                height: '100%',
              }}
            />
          </YTYStack>
        </AnimatedView>
      </GestureDetector>
    </YTYStack>
  );
};
