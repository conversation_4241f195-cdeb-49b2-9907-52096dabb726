import { YTImage, YTText, YTXStack } from '@bookln/cross-platform-components';
import { useNavigationBarHeight } from '@jgl/biz-func';
import { AcIcon } from '@jgl/icon/src';
import { JglGridView } from '@jgl/ui-v4';
import { useItemAutoFlex } from '@jgl/utils';
import { useMount } from 'ahooks';
import { Key, memo, useCallback, useMemo, useRef, useState } from 'react';
import {
  Keyboard,
  Platform,
  Pressable,
  StyleSheet,
  useWindowDimensions,
  View,
} from 'react-native';
import { XStack } from 'tamagui';
import { ChooseOptionDTO } from '../api/dto';

type Props = {
  open: boolean;
  options: ChooseOptionDTO[];
  title: string;
  value?: Key;
  onChange?: (val: Key) => void;
  onToggleShowSelect?: () => void;
  onDismissSelect?: () => void;
};

const buttonHeight = 44;

/** 年级学科版本等筛选选择组件 */
export const Select = memo((props: Props) => {
  const {
    open,
    title = '',
    value,
    onChange,
    options = [],
    onToggleShowSelect,
    onDismissSelect,
  } = props;
  const [selectedVal, setSelectedVal] = useState<Key>('');
  const selectRef = useRef<any>(null);

  useMount(() => {
    if (value !== undefined) {
      setSelectedVal(value);
    }
  });

  // kept the hook usage in case layout-based sizing is reintroduced
  useItemAutoFlex({
    minColumn: 3,
    itemMinGap: 12,
    itemMaxWidth: 110,
    extraWidth: 32,
  });

  const onSelect = useCallback(
    (key: Key) => {
      if (key === selectedVal) {
        setSelectedVal('');
      } else {
        setSelectedVal(key);
      }
      // notify parent immediately
      onChange?.(key === selectedVal ? ('' as Key) : key);
    },
    [selectedVal, onChange],
  );

  const label = useMemo(() => {
    const option = options.find((item) => item.code === selectedVal);
    return option?.name ?? title;
  }, [options, selectedVal, title]);

  const identityBox = useCallback(
    (param: { name: string; i: number; key: Key }) => {
      const { name, key } = param;
      const active = key === selectedVal;
      return (
        <YTXStack
          key={key}
          // width='30%'
          // flex={1 / 3}
          // marginBottom={8}
          // marginRight={i % 3 === 2 ? 0 : '5%'}
          minHeight={40}
          backgroundColor={
            active ? 'rgba(230,244,255,1)' : 'rgba(250,250,251,1)'
          }
          borderWidth={active ? 1 : 0}
          borderColor={active ? '#69B1FF' : 'transparent'}
          borderRadius={10}
          paddingHorizontal={12}
          paddingVertical={2}
          alignItems='center'
          justifyContent='center'
          onPress={() => onSelect(key)}
        >
          <YTText
            fontSize={14}
            color={active ? '#1677FF' : '#333333'}
            textAlign='center'
            numberOfLines={1}
          >
            {name}
          </YTText>
        </YTXStack>
      );
    },
    [onSelect, selectedVal],
  );

  const showModal = useCallback(() => {
    // setOpen((prev) => !prev);
    onToggleShowSelect?.();
  }, [onToggleShowSelect]);

  const activeColor = useMemo(() => (open ? '#1677FF' : '#333333'), [open]);

  const navigationBarHeightFromHooks = useNavigationBarHeight();
  const navigationBarHeight =
    Platform.OS === 'ios' ? navigationBarHeightFromHooks : 44;

  const windowHeight = useWindowDimensions().height;

  const pressableHeight = useMemo(() => {
    return windowHeight - buttonHeight - navigationBarHeight;
  }, [navigationBarHeight, windowHeight]);

  const handlePressablePressed = useCallback(() => {
    // setOpen(false);
    onDismissSelect?.();
    Keyboard.dismiss();
  }, [onDismissSelect]);

  return (
    <>
      <XStack position='relative'>
        <View
          ref={selectRef}
          collapsable={false}
          style={{ alignSelf: 'flex-start' }}
        >
          <YTXStack
            height={buttonHeight}
            minWidth={64}
            flexDirection='row'
            alignItems='center'
            onPress={showModal}
            bg='white'
          >
            <YTText fontSize={12} color={activeColor}>
              {label}
            </YTText>

            <YTImage
              height={14}
              width={14}
              source={AcIcon.ArrowDown}
              style={{ tintColor: activeColor }}
            />
          </YTXStack>
        </View>
      </XStack>

      {open && (
        <Pressable
          style={[
            styles.absoluteOverlay,
            {
              top: buttonHeight,
              height: pressableHeight,
            },
          ]}
          onPress={handlePressablePressed}
        >
          <YTXStack
            shadowColor='transparent'
            elevation={0}
            style={styles.dropdownWrapper}
          >
            <YTXStack
              position='absolute'
              backgroundColor='white'
              borderRadius={0}
              borderWidth={1}
              borderColor='#E5E7EB'
              overflow='hidden'
              paddingVertical={16}
              width={'$full'}
              minHeight={120}
              style={styles.dropdownContainer}
            >
              <JglGridView
                minColumns={3}
                minItemWidth={110}
                horizontalSpace={12}
                gap={12}
                paddingLeft={6}
                style={{ width: '100%' }}
              >
                {options.map((item, index) => {
                  return identityBox({
                    name: item.name ?? '',
                    i: index,
                    key: item.code as string,
                  });
                })}
              </JglGridView>
            </YTXStack>
          </YTXStack>
        </Pressable>
      )}
    </>
  );
});

const styles = StyleSheet.create({
  modal: {
    shadowColor: 'transparent',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
  },
  absoluteOverlay: {
    zIndex: 9999,
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    // backgroundColor provides dimming, but Pressable will only cover area below the button
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
  },
  dropdownWrapper: {
    // make the dropdown content not expand beyond its content height
    width: '100%',
  },
  dropdownContainer: {
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 0,
  },
  grid: {
    width: '100%',
  },
});
