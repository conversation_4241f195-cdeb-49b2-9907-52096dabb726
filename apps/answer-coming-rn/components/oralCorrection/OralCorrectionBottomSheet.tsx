import {
  YTText,
  YTTouchable,
  YTView,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { X } from '@bookln/icon-lucide';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import React, { forwardRef, useCallback, useMemo } from 'react';
import { MarkdownResult } from '../MarkdownResult';

interface AnswerItem {
  /** 识别结果 */
  recValue: string;
  /** 正确答案 */
  value: string;
  /** 类型 */
  type?: number;
}

interface OralCorrectionBottomSheetProps {
  answerItems: AnswerItem[];
}

export interface OralCorrectionBottomSheetRef {
  present: () => void;
  dismiss: () => void;
}

export const OralCorrectionBottomSheet = forwardRef<
  OralCorrectionBottomSheetRef,
  OralCorrectionBottomSheetProps
>(({ answerItems }, ref) => {
  const bottomSheetRef = React.useRef<BottomSheetModal>(null);

  const snapPoints = useMemo(() => ['50%'], []);

  const present = useCallback(() => {
    bottomSheetRef.current?.present();
  }, []);

  const dismiss = useCallback(() => {
    bottomSheetRef.current?.dismiss();
  }, []);

  React.useImperativeHandle(ref, () => ({
    present,
    dismiss,
  }));

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.6}
      />
    ),
    [],
  );

  const renderHandle = useCallback(() => {
    return (
      <YTXStack w='100%' alignItems='center' justifyContent='flex-end'>
        <YTTouchable
          alignItems='center'
          justifyContent='center'
          p={12}
          onPress={dismiss}
        >
          <X size={20} color='#666' />
        </YTTouchable>
      </YTXStack>
    );
  }, [dismiss]);

  return (
    <BottomSheetModal
      ref={bottomSheetRef}
      snapPoints={snapPoints}
      backdropComponent={renderBackdrop}
      handleComponent={renderHandle}
      enableDismissOnClose
      enablePanDownToClose
      enableContentPanningGesture={false}
      enableHandlePanningGesture={false}
    >
      <BottomSheetScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingBottom: 40,
        }}
      >
        <YTYStack gap={20}>
          {/* 识别结果 */}
          <YTYStack gap={8}>
            <YTText fontSize={16} fontWeight='bold' color='#1f1f1f'>
              识别结果
            </YTText>
            <YTView flexDirection='row' flexWrap='wrap' gap={8}>
              {answerItems.map((item, index) => (
                <YTView
                  key={`rec-${index}`}
                  bg='#f5f5f5'
                  borderRadius={8}
                  px={12}
                  py={8}
                >
                  <MarkdownResult
                    content={`$${item.recValue}$`}
                    isSupportLatex
                    showAIEndTip={false}
                  />
                </YTView>
              ))}
            </YTView>
          </YTYStack>

          {/* 正确答案 */}
          <YTYStack gap={8}>
            <YTText fontSize={16} fontWeight='bold' color='#1f1f1f'>
              正确答案
            </YTText>
            <YTView flexDirection='row' flexWrap='wrap' gap={8}>
              {answerItems.map((item, index) => (
                <YTView
                  key={`correct-${index}`}
                  bg='#f0f9ff'
                  borderRadius={8}
                  px={12}
                  py={8}
                  borderWidth={1}
                  borderColor='#1890ff'
                >
                  <MarkdownResult
                    content={`$${item.value}$`}
                    isSupportLatex
                    showAIEndTip={false}
                  />
                </YTView>
              ))}
            </YTView>
          </YTYStack>
        </YTYStack>
      </BottomSheetScrollView>
    </BottomSheetModal>
  );
});

OralCorrectionBottomSheet.displayName = 'OralCorrectionBottomSheet';
